/**
 * 视觉脚本系统模块
 * 导出所有视觉脚本系统相关的类和接口
 */

// 核心系统
export * from './VisualScriptSystem';
export * from './VisualScriptEngine';
export * from './VisualScriptComponent';

// 节点系统
export * from './nodes/Node';
export * from './nodes/FlowNode';
export * from './nodes/EventNode';
export * from './nodes/FunctionNode';
export * from './nodes/AsyncNode';
export * from './nodes/NodeRegistry';

// 图形系统
export * from './graph/Graph';
export * from './graph/GraphJSON';

// 执行系统
export * from './execution/Fiber';
export * from './execution/ExecutionContext';

// 事件系统
export * from './events/CustomEvent';

// 值类型系统
export * from './values/ValueTypeRegistry';
export * from './values/Variable';

// 工具类
// 注意：Logger 和 Assert 工具类文件不存在，已移除导出

// 编辑器集成
// 注意：NodeEditor 和 GraphEditor 文件不存在，已移除导出

// 预设节点
export * from './presets/CoreNodes';
export * from './presets/MathNodes';
export * from './presets/EntityNodes';
export * from './presets/PhysicsNodes';
export * from './presets/AnimationNodes';
export * from './presets/InputNodes';
export * from './presets/AudioNodes';
export * from './presets/NetworkNodes';
export * from './presets/TransformNodes';
export * from './presets/SceneNodes';

// 逻辑节点 - 显式导出以避免与CoreNodes冲突
export {
  ComparisonNode,
  LogicalOperationNode,
  ToggleNode,
  BranchNode as LogicBranchNode  // 重命名以避免冲突
} from './presets/LogicNodes';

// 时间节点 - 显式导出新实现的节点
export {
  GetCurrentTimeNode,
  GetDeltaTimeNode,
  DelayNode,
  TimerNode,
  StopwatchNode,
  FormatTimeNode
} from './presets/TimeNodes';

// 动画节点 - 显式导出新实现的节点
export {
  PlayAnimationNode,
  StopAnimationNode,
  PauseAnimationNode,
  ResumeAnimationNode,
  SetAnimationSpeedNode,
  GetAnimationStateNode,
  SetAnimationTimeNode,
  CrossFadeAnimationNode
} from './presets/AnimationNodes';

// 输入节点 - 显式导出新实现的节点
export {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode,
  IsKeyDownNode,
  IsKeyUpNode,
  // 新增输入处理节点 (152-165)
  OnKeyPressNode,
  GetMousePositionNode,
  IsMouseButtonDownNode,
  OnMouseClickNode,
  OnMouseMoveNode,
  OnMouseWheelNode,
  GetTouchCountNode,
  GetTouchPositionNode,
  OnTouchStartNode,
  OnTouchEndNode,
  OnTouchMoveNode,
  IsGamepadConnectedNode,
  GetGamepadButtonStateNode,
  GetGamepadAxisValueNode
} from './presets/InputNodes';

// 物理节点 - 显式导出新实现的节点 (196-207)
export {
  RaycastNode,
  ApplyForceNode,
  ApplyImpulseNode,
  SetVelocityNode,
  GetVelocityNode,
  SetMassNode,
  GetMassNode,
  OnCollisionEnterNode,
  OnCollisionExitNode,
  OnTriggerEnterNode,
  OnTriggerExitNode,
  SetGravityNode
} from './presets/PhysicsNodes';

// 音频节点 - 注册函数导出
export { registerAudioNodes } from './presets/AudioNodes';

// AI高级功能节点 - 显式导出新实现的节点 (286-295)
export {
  PathfindingNode,
  FollowPathNode,
  AIStateMachineNode,
  AIDecisionTreeNode,
  TextAnalysisNode,
  SpeechToTextNode,
  TextToSpeechNode,
  ObjectDetectionNode,
  FaceRecognitionNode,
  GenerateMotionNode,
  registerAIAdvancedNodes
} from './presets/AIAdvancedNodes';

// 调试工具节点 - 显式导出新实现的节点 (296-300)
export {
  MemoryUsageNode
} from './presets/DebugNodes';

// UI节点 - 显式导出新实现的节点 (241-249)
export {
  CreateSliderNode,
  GetSliderValueNode,
  SetSliderValueNode,
  SliderValueChangeNode,
  CreateImageNode,
  SetImageTextureNode,
  CreatePanelNode,
  AddChildToPanel,
  RemoveChildFromPanel
} from './presets/UINodes';

// 事件系统节点 - 显式导出新实现的节点 (250-255)
export {
  CreateCustomEventNode,
  TriggerEventNode,
  ListenEventNode,
  OnSystemStartNode,
  OnSystemUpdateNode,
  OnSystemDestroyNode
} from './presets/CoreNodes';

// HTTP请求节点 - 显式导出新实现的节点 (256-259)
export {
  HTTPGetNode,
  HTTPPostNode,
  HTTPPutNode,
  HTTPDeleteNode,
  registerHTTPNodes
} from './presets/HTTPNodes';

// WebSocket通信节点 - 显式导出新实现的节点 (260-263)
export {
  WebSocketConnectNode,
  WebSocketSendNode,
  WebSocketOnMessageNode,
  WebSocketDisconnectNode,
  registerWebSocketNodes
} from './presets/WebSocketNodes';

// WebRTC数据通信节点 - 显式导出新实现的节点 (264-266)
export {
  WebRTCCreateConnectionNode,
  WebRTCSendDataNode,
  WebRTCOnDataReceivedNode,
  registerWebRTCDataNodes
} from './presets/WebRTCDataNodes';

// JSON数据处理节点 - 显式导出新实现的节点 (267-268)
export {
  JSONParseNode,
  JSONStringifyNode,
  registerJSONNodes
} from './presets/JSONNodes';

// 本地存储节点 - 显式导出新实现的节点 (269-271)
export {
  LocalStorageSetNode,
  LocalStorageGetNode,
  LocalStorageRemoveNode,
  registerLocalStorageNodes
} from './presets/LocalStorageNodes';

// 会话存储节点 - 显式导出新实现的节点 (272-273)
export {
  SessionStorageSetNode,
  SessionStorageGetNode,
  registerSessionStorageNodes
} from './presets/SessionStorageNodes';

// 数据库操作节点 - 显式导出新实现的节点 (274-277)
export {
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  registerDatabaseNodes
} from './presets/DatabaseNodes';

// 文件操作节点 - 显式导出新实现的节点 (278-282)
export {
  LoadFileNode,
  SaveFileNode,
  FileExistsNode,
  GetFileSizeNode,
  GetFileExtensionNode,
  registerFileNodes
} from './presets/FileNodes';

// 资产管理节点 - 显式导出新实现的节点 (283-285)
export {
  LoadAssetNode,
  UnloadAssetNode,
  GetAssetProgressNode,
  registerAssetNodes
} from './presets/AssetNodes';
